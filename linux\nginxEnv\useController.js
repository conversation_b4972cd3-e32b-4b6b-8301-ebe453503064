import { ref, reactive, computed } from 'vue';

// 页面引用
export const pageContainer = ref(null);

// 服务状态
export const serviceStatus = reactive({
	isRunning: true,
	version: 'nginx 1.24',
});

export const serviceLoading = ref(false);

// 快速操作配置 - 动态计算
export const quickActions = computed(() => {
	const actions = [];

	if (serviceStatus.isRunning) {
		// 服务运行时显示：停止、重启、重载配置
		actions.push(
			{
				id: 'stop',
				title: '停止',
				icon: 'icon-stop',
				useIconfont: true,
			},
			{
				id: 'restart',
				title: '重启',
				icon: 'icon-restart',
				useIconfont: true,
			},
			{
				id: 'reload',
				title: '重载配置',
				icon: 'icon-reload',
				useIconfont: true,
			},
		);
	} else {
		// 服务停止时显示：启动
		actions.push({
			id: 'start',
			title: '启动',
			icon: 'icon-start',
			useIconfont: true,
		});
	}

	return actions;
});

// 功能模块配置 - 根据PC端截图的6个功能
export const functionModules = ref([
	{
		id: 'config',
		title: '配置文件',
		desc: '编辑nginx.conf主配置文件',
		icon: 'icon-config',
		useIconfont: true,
		showData: false,
	},
	{
		id: 'version',
		title: '版本切换',
		desc: '切换不同的nginx版本',
		icon: 'icon-version',
		useIconfont: true,
		showData: true,
		dataValue: 'nginx 1.24',
		dataUnit: '',
	},
	{
		id: 'status',
		title: '负载状态',
		desc: '查看连接数和请求统计',
		icon: 'icon-status',
		useIconfont: true,
		showData: true,
		dataValue: '1',
		dataUnit: '个活跃连接',
	},
	{
		id: 'performance',
		title: '性能调整',
		desc: '调整worker进程和连接数等参数',
		icon: 'icon-performance',
		useIconfont: true,
		showData: false,
	},
	{
		id: 'errorLog',
		title: '错误日志',
		desc: '查看nginx错误日志',
		icon: 'icon-log',
		useIconfont: true,
		showData: false,
	},
]);

// 版本列表
export const availableVersions = ref([
	'nginx 1.24',
	'nginx 1.26',
	'nginx 1.22',
	'nginx 1.28',
	'nginx -Tengine3.1',
	'nginx 1.21',
	'nginx openresty',
	'nginx 1.20',
]);

// 初始化数据
export const initNginxEnvData = async () => {
	try {
		// 模拟API调用获取服务状态
		await mockGetServiceStatus();
		// 模拟API调用获取负载状态数据
		await mockGetLoadStatus();
	} catch (error) {
		console.error('初始化Nginx环境数据失败:', error);
	}
};

// 模拟API - 获取服务状态
const mockGetServiceStatus = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			// 模拟随机状态
			const isRunning = Math.random() > 0.3;
			serviceStatus.isRunning = isRunning;
			resolve();
		}, 500);
	});
};

// 模拟API - 获取负载状态数据
const mockGetLoadStatus = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			// 更新负载状态模块的数据
			const statusModule = functionModules.value.find((m) => m.id === 'status');
			if (statusModule) {
				const connections = Math.floor(Math.random() * 10) + 1;
				statusModule.dataValue = connections.toString();
				statusModule.dataUnit = '个活跃连接';
			}
			resolve();
		}, 300);
	});
};

// 服务控制
export const toggleService = async (value) => {
	try {
		serviceLoading.value = true;
		// 模拟API调用
		await new Promise((resolve) => setTimeout(resolve, 1000));

		serviceStatus.isRunning = value;
		if (value) {
			pageContainer.value?.notify?.success('Nginx服务启动成功');
		} else {
			pageContainer.value?.notify?.success('Nginx服务停止成功');
		}
	} catch (error) {
		console.error('服务状态切换失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
	} finally {
		serviceLoading.value = false;
	}
};

// 快速操作处理
export const handleQuickAction = async (action) => {
	try {
		switch (action.id) {
			case 'start':
				await toggleService(true);
				break;
			case 'stop':
				await toggleService(false);
				break;
			case 'restart':
				pageContainer.value?.notify?.info('正在重启Nginx服务...');
				serviceLoading.value = true;
				await new Promise((resolve) => setTimeout(resolve, 2000));
				// 重启后确保服务是运行状态
				serviceStatus.isRunning = true;
				await mockGetLoadStatus();
				serviceLoading.value = false;
				pageContainer.value?.notify?.success('Nginx服务重启成功');
				break;
			case 'reload':
				pageContainer.value?.notify?.info('正在重载配置...');
				serviceLoading.value = true;
				await new Promise((resolve) => setTimeout(resolve, 1000));
				serviceLoading.value = false;
				pageContainer.value?.notify?.success('配置重载成功');
				break;
		}
	} catch (error) {
		console.error('快速操作失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
		serviceLoading.value = false;
	}
};

// 模块点击处理
export const handleModuleClick = async (module) => {
	try {
		switch (module.id) {
			case 'config':
				pageContainer.value?.notify?.info('打开配置文件编辑器');
				// 这里可以导航到配置编辑页面
				break;
			case 'version':
				// 显示版本选择
				showVersionSelector();
				break;
			case 'status':
				pageContainer.value?.notify?.info('刷新负载状态...');
				await mockGetLoadStatus();
				pageContainer.value?.notify?.success('负载状态已更新');
				break;
			case 'performance':
				pageContainer.value?.notify?.info('打开性能调整页面');
				// 这里可以导航到性能调整页面
				break;
			case 'errorLog':
				pageContainer.value?.notify?.info('打开错误日志页面');
				// 这里可以导航到错误日志页面
				break;
		}
	} catch (error) {
		console.error('模块操作失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
	}
};

// 显示版本选择器
const showVersionSelector = () => {
	uni.showActionSheet({
		itemList: availableVersions.value,
		success: async (res) => {
			const selectedVersion = availableVersions.value[res.tapIndex];
			if (selectedVersion !== serviceStatus.version) {
				try {
					pageContainer.value?.notify?.info(`正在切换到 ${selectedVersion}...`);
					await new Promise((resolve) => setTimeout(resolve, 2000));

					serviceStatus.version = selectedVersion;
					// 更新版本模块显示
					const versionModule = functionModules.value.find((m) => m.id === 'version');
					if (versionModule) {
						versionModule.dataValue = selectedVersion;
					}

					pageContainer.value?.notify?.success(`已成功切换到 ${selectedVersion}`);
				} catch (error) {
					console.error('版本切换失败:', error);
					pageContainer.value?.notify?.error('版本切换失败，请重试');
				}
			} else {
				pageContainer.value?.notify?.info('当前已是此版本');
			}
		},
	});
};
