import { ref, reactive } from 'vue';

// 页面引用
export const pageContainer = ref(null);
export const versionPopup = ref(null);

// 服务状态
export const serviceStatus = reactive({
	isRunning: true,
	version: 'nginx 1.24',
	uptime: '3天 12小时',
	pid: 1234
});

export const serviceLoading = ref(false);

// 快速操作配置
export const quickActions = [
	{
		icon: 'icon-start',
		title: '启动',
		action: 'start',
		useIconfont: true
	},
	{
		icon: 'icon-stop',
		title: '停止',
		action: 'stop',
		useIconfont: true
	},
	{
		icon: 'icon-restart',
		title: '重启',
		action: 'restart',
		useIconfont: true
	},
	{
		icon: 'reload',
		title: '重载',
		action: 'reload',
		useIconfont: false
	},
	{
		icon: 'icon-test',
		title: '测试配置',
		action: 'test',
		useIconfont: true
	},
	{
		icon: 'icon-status',
		title: '状态检查',
		action: 'status',
		useIconfont: true
	},
	{
		icon: 'icon-backup',
		title: '备份配置',
		action: 'backup',
		useIconfont: true
	},
	{
		icon: 'icon-update',
		title: '更新',
		action: 'update',
		useIconfont: true
	}
];

// 性能监控数据
export const performanceMetrics = ref([
	{
		icon: 'icon-connect',
		title: '活跃连接',
		value: '1',
		unit: '个',
		useIconfont: true
	},
	{
		icon: 'icon-request',
		title: '总请求数',
		value: '4',
		unit: '次',
		useIconfont: true
	},
	{
		icon: 'icon-cpu',
		title: 'CPU使用',
		value: '0',
		unit: '%',
		useIconfont: true
	},
	{
		icon: 'icon-memory',
		title: '内存占用',
		value: '135MB',
		unit: '',
		useIconfont: true
	}
]);

// 版本管理
export const currentVersion = ref('nginx 1.24');
export const availableVersions = ref([
	'nginx 1.24',
	'nginx 1.26',
	'nginx 1.22',
	'nginx 1.28',
	'nginx -Tengine3.1',
	'nginx 1.21',
	'nginx openresty',
	'nginx 1.20'
]);

// 配置管理
export const configActions = [
	{
		icon: 'icon-config',
		title: '主配置文件',
		desc: '编辑nginx.conf主配置',
		action: 'mainConfig',
		useIconfont: true
	},
	{
		icon: 'icon-performance',
		title: '性能调整',
		desc: '优化性能参数设置',
		action: 'performance',
		useIconfont: true
	},
	{
		icon: 'icon-log',
		title: '日志配置',
		desc: '配置访问和错误日志',
		action: 'logConfig',
		useIconfont: true
	},
	{
		icon: 'icon-ssl',
		title: 'SSL配置',
		desc: '管理SSL证书和配置',
		action: 'sslConfig',
		useIconfont: true
	}
];

// 最近日志
export const recentLogs = ref([
	{
		time: '17:00:17',
		content: 'bind() to 0.0.0.0:5465 failed (98: Address already in use)',
		level: 'error'
	},
	{
		time: '17:00:17',
		content: 'worker_connections 51200',
		level: 'info'
	},
	{
		time: '17:00:17',
		content: 'still could not bind()',
		level: 'warn'
	}
]);

// 初始化数据
export const initNginxEnvData = async () => {
	try {
		// 模拟API调用获取服务状态
		await mockGetServiceStatus();
		// 模拟API调用获取性能数据
		await mockGetPerformanceData();
		// 模拟API调用获取日志数据
		await mockGetRecentLogs();
	} catch (error) {
		console.error('初始化Nginx环境数据失败:', error);
	}
};

// 模拟API - 获取服务状态
const mockGetServiceStatus = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			// 模拟随机状态
			const isRunning = Math.random() > 0.3;
			serviceStatus.isRunning = isRunning;
			serviceStatus.pid = isRunning ? Math.floor(Math.random() * 9999) + 1000 : null;
			resolve();
		}, 500);
	});
};

// 模拟API - 获取性能数据
const mockGetPerformanceData = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			performanceMetrics.value[0].value = Math.floor(Math.random() * 10) + 1;
			performanceMetrics.value[1].value = Math.floor(Math.random() * 100) + 1;
			performanceMetrics.value[2].value = Math.floor(Math.random() * 50);
			performanceMetrics.value[3].value = Math.floor(Math.random() * 200) + 100 + 'MB';
			resolve();
		}, 300);
	});
};

// 模拟API - 获取最近日志
const mockGetRecentLogs = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			const mockLogs = [
				{ content: 'bind() to 0.0.0.0:5465 failed (98: Address already in use)', level: 'error' },
				{ content: 'worker_connections 51200', level: 'info' },
				{ content: 'still could not bind()', level: 'warn' },
				{ content: 'nginx started successfully', level: 'info' },
				{ content: 'configuration file test passed', level: 'info' }
			];
			
			recentLogs.value = mockLogs.slice(0, 3).map(log => ({
				...log,
				time: new Date().toLocaleTimeString('zh-CN', { hour12: false }).slice(0, 8)
			}));
			resolve();
		}, 200);
	});
};

// 服务控制
export const toggleService = async (value) => {
	try {
		serviceLoading.value = true;
		// 模拟API调用
		await new Promise(resolve => setTimeout(resolve, 1000));
		
		serviceStatus.isRunning = value;
		if (value) {
			serviceStatus.pid = Math.floor(Math.random() * 9999) + 1000;
			pageContainer.value?.notify?.success('Nginx服务启动成功');
		} else {
			serviceStatus.pid = null;
			pageContainer.value?.notify?.success('Nginx服务停止成功');
		}
	} catch (error) {
		console.error('服务状态切换失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
	} finally {
		serviceLoading.value = false;
	}
};

// 快速操作处理
export const handleQuickAction = async (action) => {
	try {
		switch (action.action) {
			case 'start':
				await toggleService(true);
				break;
			case 'stop':
				await toggleService(false);
				break;
			case 'restart':
				pageContainer.value?.notify?.info('正在重启Nginx服务...');
				await new Promise(resolve => setTimeout(resolve, 2000));
				await mockGetServiceStatus();
				pageContainer.value?.notify?.success('Nginx服务重启成功');
				break;
			case 'reload':
				pageContainer.value?.notify?.info('正在重载配置...');
				await new Promise(resolve => setTimeout(resolve, 1000));
				pageContainer.value?.notify?.success('配置重载成功');
				break;
			case 'test':
				pageContainer.value?.notify?.info('正在测试配置...');
				await new Promise(resolve => setTimeout(resolve, 800));
				pageContainer.value?.notify?.success('配置文件语法正确');
				break;
			case 'status':
				await mockGetServiceStatus();
				pageContainer.value?.notify?.success('状态检查完成');
				break;
			case 'backup':
				pageContainer.value?.notify?.info('正在备份配置...');
				await new Promise(resolve => setTimeout(resolve, 1500));
				pageContainer.value?.notify?.success('配置备份完成');
				break;
			case 'update':
				pageContainer.value?.notify?.info('检查更新中...');
				await new Promise(resolve => setTimeout(resolve, 1000));
				pageContainer.value?.notify?.info('当前已是最新版本');
				break;
		}
	} catch (error) {
		console.error('快速操作失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
	}
};

// 刷新性能数据
export const refreshPerformance = async () => {
	try {
		pageContainer.value?.notify?.info('正在刷新性能数据...');
		await mockGetPerformanceData();
		pageContainer.value?.notify?.success('性能数据已更新');
	} catch (error) {
		console.error('刷新性能数据失败:', error);
		pageContainer.value?.notify?.error('刷新失败，请重试');
	}
};

// 版本管理
export const showVersionManager = () => {
	versionPopup.value?.open();
};

export const closeVersionManager = () => {
	versionPopup.value?.close();
};

export const switchVersion = async (version) => {
	try {
		if (version === currentVersion.value) {
			pageContainer.value?.notify?.info('当前已是此版本');
			return;
		}
		
		pageContainer.value?.notify?.info(`正在切换到 ${version}...`);
		await new Promise(resolve => setTimeout(resolve, 2000));
		
		currentVersion.value = version;
		serviceStatus.version = version;
		
		pageContainer.value?.notify?.success(`已成功切换到 ${version}`);
		closeVersionManager();
	} catch (error) {
		console.error('版本切换失败:', error);
		pageContainer.value?.notify?.error('版本切换失败，请重试');
	}
};

// 配置管理
export const handleConfigAction = (config) => {
	switch (config.action) {
		case 'mainConfig':
			pageContainer.value?.notify?.info('打开主配置文件编辑器');
			// 这里可以导航到配置编辑页面
			break;
		case 'performance':
			pageContainer.value?.notify?.info('打开性能调整页面');
			// 这里可以导航到性能调整页面
			break;
		case 'logConfig':
			pageContainer.value?.notify?.info('打开日志配置页面');
			// 这里可以导航到日志配置页面
			break;
		case 'sslConfig':
			pageContainer.value?.notify?.info('打开SSL配置页面');
			// 这里可以导航到SSL配置页面
			break;
	}
};

// 查看全部日志
export const viewAllLogs = () => {
	pageContainer.value?.notify?.info('打开日志查看页面');
	// 这里可以导航到日志查看页面
};
