<template>
	<page-container ref="pageContainer" :is-back="true" title="Nginx环境管理">
		<view class="nginx-env-container">
			<!-- 服务状态卡片 -->
			<view class="status-card">
				<view class="status-header">
					<view class="status-info">
						<text class="status-title">Nginx服务</text>
						<text class="status-desc">当前运行状态</text>
					</view>
					<view class="status-control">
						<text class="status-badge" :class="serviceStatus.isRunning ? 'badge-success' : 'badge-danger'">
							{{ serviceStatus.isRunning ? '运行中' : '已停止' }}
						</text>
						<uv-switch
							:model-value="serviceStatus.isRunning"
							size="24"
							activeColor="#20a50a"
							:loading="serviceLoading"
							@change="toggleService"
						></uv-switch>
					</view>
				</view>
			</view>

			<!-- 功能模块 -->
			<view class="function-modules">
				<view
					v-for="(module, index) in functionModules"
					:key="index"
					class="module-card"
					@tap="handleModuleClick(module)"
					hover-class="module-hover"
				>
					<view class="module-header">
						<view class="module-icon">
							<text v-if="module.useIconfont" :class="['iconfont', module.icon]"></text>
							<uv-icon v-else :name="module.icon" size="20"></uv-icon>
						</view>
						<text class="module-title">{{ module.title }}</text>
					</view>
					<text class="module-desc">{{ module.desc }}</text>
					<view v-if="module.showData" class="module-data">
						<text class="data-value">{{ module.dataValue }}</text>
						<text class="data-unit">{{ module.dataUnit }}</text>
					</view>
				</view>
			</view>


		</view>


	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import { onShow } from '@dcloudio/uni-app';
	import {
		pageContainer,
		serviceStatus,
		serviceLoading,
		functionModules,
		toggleService,
		handleModuleClick,
		initNginxEnvData
	} from './useController.js';

	onShow(async () => {
		await initNginxEnvData();
	});
</script>

<style lang="scss" scoped>
	.nginx-env-container {
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		gap: 32rpx;
		background-color: var(--page-bg-color);
		min-height: 100vh;
	}

	/* 服务状态卡片 */
	.status-card {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	.status-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.status-info {
		.status-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-primary-color);
			display: block;
		}

		.status-desc {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			display: block;
			margin-top: 8rpx;
		}
	}

	.status-control {
		display: flex;
		align-items: center;
		gap: 24rpx;
	}

	.status-badge {
		font-size: 24rpx;
		font-weight: 500;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;

		&.badge-success {
			background-color: #dcfce7;
			color: #16a34a;
		}

		&.badge-danger {
			background-color: #fee2e2;
			color: #dc2626;
		}
	}

	/* 功能模块 */
	.function-modules {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
	}

	.module-card {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
		transition: all 0.2s ease;
	}

	.module-hover {
		background-color: var(--page-bg-color);
		transform: translateY(-2rpx);
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
	}

	.module-header {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 12rpx;

		.module-icon {
			width: 48rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgba(0, 122, 255, 0.1);
			border-radius: 12rpx;

			.iconfont {
				font-size: 28rpx;
				color: #007aff;
			}
		}

		.module-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-primary-color);
		}
	}

	.module-desc {
		font-size: 26rpx;
		color: var(--text-secondary-color);
		margin-bottom: 16rpx;
		line-height: 1.4;
	}

	.module-data {
		display: flex;
		align-items: baseline;
		gap: 8rpx;
		padding: 16rpx;
		background-color: var(--page-bg-color);
		border-radius: 8rpx;

		.data-value {
			font-size: 36rpx;
			font-weight: 600;
			color: var(--text-primary-color);
		}

		.data-unit {
			font-size: 24rpx;
			color: var(--text-secondary-color);
		}
	}

	/* 响应式调整 */
	@media (max-width: 360px) {
		.function-modules {
			gap: 16rpx;
		}

		.module-card {
			padding: 24rpx;
		}
	}
</style>