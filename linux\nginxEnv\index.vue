<template>
	<page-container ref="pageContainer" :is-back="true" title="Nginx环境管理">
		<view class="nginx-env-container">
			<!-- 服务状态卡片 -->
			<view class="status-card">
				<view class="status-header">
					<view class="status-info">
						<text class="status-title">Nginx服务</text>
						<text class="status-desc">当前运行状态</text>
					</view>
					<view class="status-control">
						<text class="status-badge" :class="serviceStatus.isRunning ? 'badge-success' : 'badge-danger'">
							{{ serviceStatus.isRunning ? '运行中' : '已停止' }}
						</text>
						<uv-switch
							:model-value="serviceStatus.isRunning"
							size="24"
							activeColor="#20a50a"
							:loading="serviceLoading"
							@change="toggleService"
						></uv-switch>
					</view>
				</view>

				<!-- 服务信息 -->
				<view class="service-info">
					<view class="info-item">
						<text class="info-label">版本</text>
						<text class="info-value">{{ serviceStatus.version }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">运行时间</text>
						<text class="info-value">{{ serviceStatus.uptime }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">PID</text>
						<text class="info-value">{{ serviceStatus.pid || '-' }}</text>
					</view>
				</view>
			</view>

			<!-- 快速操作 -->
			<view class="quick-actions">
				<text class="section-title">快速操作</text>
				<view class="actions-grid">
					<view
						v-for="(action, index) in quickActions"
						:key="index"
						class="action-item"
						@tap="handleQuickAction(action)"
						hover-class="action-hover"
					>
						<view class="action-icon">
							<text v-if="action.useIconfont" :class="['iconfont', action.icon]"></text>
							<uv-icon v-else :name="action.icon" size="20"></uv-icon>
						</view>
						<text class="action-title">{{ action.title }}</text>
					</view>
				</view>
			</view>

			<!-- 性能监控 -->
			<view class="performance-section">
				<view class="section-header">
					<text class="section-title">性能监控</text>
					<text class="refresh-btn" @tap="refreshPerformance">刷新</text>
				</view>
				<view class="metrics-grid">
					<view
						v-for="(metric, index) in performanceMetrics"
						:key="index"
						class="metric-card"
					>
						<view class="metric-header">
							<text v-if="metric.useIconfont" :class="['iconfont', metric.icon]"></text>
							<uv-icon v-else :name="metric.icon" size="16"></uv-icon>
							<text class="metric-title">{{ metric.title }}</text>
						</view>
						<text class="metric-value">{{ metric.value }}</text>
						<text class="metric-unit">{{ metric.unit }}</text>
					</view>
				</view>
			</view>

			<!-- 版本管理 -->
			<view class="version-section">
				<view class="section-header">
					<text class="section-title">版本管理</text>
					<text class="manage-btn" @tap="showVersionManager">管理</text>
				</view>
				<view class="version-info">
					<view class="current-version">
						<text class="version-label">当前版本</text>
						<text class="version-value">{{ currentVersion }}</text>
					</view>
					<view class="available-versions">
						<text class="versions-label">可用版本</text>
						<view class="versions-list">
							<text
								v-for="(version, index) in availableVersions.slice(0, 3)"
								:key="index"
								class="version-tag"
								@tap="switchVersion(version)"
							>
								{{ version }}
							</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 配置管理 -->
			<view class="config-section">
				<text class="section-title">配置管理</text>
				<view class="config-actions">
					<view
						v-for="(config, index) in configActions"
						:key="index"
						class="config-item"
						@tap="handleConfigAction(config)"
						hover-class="config-hover"
					>
						<view class="config-icon">
							<text v-if="config.useIconfont" :class="['iconfont', config.icon]"></text>
							<uv-icon v-else :name="config.icon" size="18"></uv-icon>
						</view>
						<view class="config-content">
							<text class="config-title">{{ config.title }}</text>
							<text class="config-desc">{{ config.desc }}</text>
						</view>
						<uv-icon name="arrow-right" size="14" color="#999"></uv-icon>
					</view>
				</view>
			</view>

			<!-- 日志监控 -->
			<view class="log-section">
				<view class="section-header">
					<text class="section-title">日志监控</text>
					<text class="view-all-btn" @tap="viewAllLogs">查看全部</text>
				</view>
				<view class="log-preview">
					<view
						v-for="(log, index) in recentLogs"
						:key="index"
						class="log-item"
					>
						<view class="log-time">{{ log.time }}</view>
						<view class="log-content">{{ log.content }}</view>
						<view class="log-level" :class="'level-' + log.level">{{ log.level }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 版本管理弹窗 -->
		<uv-popup
			ref="versionPopup"
			mode="bottom"
			:round="20"
			:safe-area-inset-bottom="true"
		>
			<view class="version-popup">
				<view class="popup-header">
					<text class="popup-title">版本管理</text>
					<text class="popup-close" @tap="closeVersionManager">完成</text>
				</view>
				<view class="version-list">
					<view
						v-for="(version, index) in availableVersions"
						:key="index"
						class="version-item"
						@tap="switchVersion(version)"
						hover-class="version-hover"
					>
						<text class="version-name">{{ version }}</text>
						<text v-if="version === currentVersion" class="current-tag">当前</text>
					</view>
				</view>
			</view>
		</uv-popup>
	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import { onShow } from '@dcloudio/uni-app';
	import {
		pageContainer,
		serviceStatus,
		serviceLoading,
		quickActions,
		performanceMetrics,
		currentVersion,
		availableVersions,
		configActions,
		recentLogs,
		versionPopup,
		toggleService,
		handleQuickAction,
		refreshPerformance,
		showVersionManager,
		closeVersionManager,
		switchVersion,
		handleConfigAction,
		viewAllLogs,
		initNginxEnvData
	} from './useController.js';

	onShow(async () => {
		await initNginxEnvData();
	});
</script>

<style lang="scss" scoped>
	.nginx-env-container {
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		gap: 32rpx;
		background-color: var(--page-bg-color);
		min-height: 100vh;
	}

	/* 服务状态卡片 */
	.status-card {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	.status-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.status-info {
		.status-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-primary-color);
			display: block;
		}

		.status-desc {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			display: block;
			margin-top: 8rpx;
		}
	}

	.status-control {
		display: flex;
		align-items: center;
		gap: 24rpx;
	}

	.status-badge {
		font-size: 24rpx;
		font-weight: 500;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;

		&.badge-success {
			background-color: #dcfce7;
			color: #16a34a;
		}

		&.badge-danger {
			background-color: #fee2e2;
			color: #dc2626;
		}
	}

	.service-info {
		display: flex;
		justify-content: space-between;
		padding-top: 24rpx;
		border-top: 2rpx solid var(--border-color);
	}

	.info-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;

		.info-label {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			margin-bottom: 8rpx;
		}

		.info-value {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-primary-color);
		}
	}

	/* 通用区块样式 */
	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: var(--text-primary-color);
		margin-bottom: 24rpx;
		display: block;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;

		.refresh-btn,
		.manage-btn,
		.view-all-btn {
			font-size: 26rpx;
			color: #007aff;
			padding: 8rpx 16rpx;
			border-radius: 8rpx;
			background-color: rgba(0, 122, 255, 0.1);
		}
	}

	/* 快速操作 */
	.quick-actions {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	.actions-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 24rpx;
	}

	.action-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 24rpx 16rpx;
		border-radius: 12rpx;
		background-color: var(--page-bg-color);
		transition: all 0.2s ease;

		.action-icon {
			width: 48rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 12rpx;

			.iconfont {
				font-size: 32rpx;
				color: #666666;
			}
		}

		.action-title {
			font-size: 22rpx;
			color: var(--text-primary-color);
			text-align: center;
		}
	}

	.action-hover {
		background-color: rgba(0, 122, 255, 0.1);
		transform: translateY(-2rpx);
	}

	/* 性能监控 */
	.performance-section {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 16rpx;
	}

	.metric-card {
		background-color: var(--page-bg-color);
		border-radius: 12rpx;
		padding: 20rpx;
		text-align: center;

		.metric-header {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 8rpx;
			margin-bottom: 12rpx;

			.metric-title {
				font-size: 24rpx;
				color: var(--text-secondary-color);
			}
		}

		.metric-value {
			font-size: 36rpx;
			font-weight: 600;
			color: var(--text-primary-color);
			display: block;
		}

		.metric-unit {
			font-size: 20rpx;
			color: var(--text-secondary-color);
			display: block;
			margin-top: 4rpx;
		}
	}

	/* 版本管理 */
	.version-section {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	.version-info {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		gap: 32rpx;
	}

	.current-version {
		flex: 1;

		.version-label {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			display: block;
			margin-bottom: 8rpx;
		}

		.version-value {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-primary-color);
		}
	}

	.available-versions {
		flex: 1;

		.versions-label {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			display: block;
			margin-bottom: 12rpx;
		}

		.versions-list {
			display: flex;
			flex-wrap: wrap;
			gap: 8rpx;
		}

		.version-tag {
			font-size: 22rpx;
			color: #007aff;
			background-color: rgba(0, 122, 255, 0.1);
			padding: 6rpx 12rpx;
			border-radius: 16rpx;
			border: 2rpx solid rgba(0, 122, 255, 0.2);
		}
	}

	/* 配置管理 */
	.config-section {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	.config-actions {
		display: flex;
		flex-direction: column;
		gap: 2rpx;
	}

	.config-item {
		display: flex;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 2rpx solid var(--border-color);
		transition: background-color 0.2s ease;

		&:last-child {
			border-bottom: none;
		}

		.config-icon {
			width: 48rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16rpx;

			.iconfont {
				font-size: 28rpx;
				color: #666666;
			}
		}

		.config-content {
			flex: 1;

			.config-title {
				font-size: 28rpx;
				color: var(--text-primary-color);
				display: block;
				margin-bottom: 4rpx;
			}

			.config-desc {
				font-size: 22rpx;
				color: var(--text-secondary-color);
			}
		}
	}

	.config-hover {
		background-color: var(--page-bg-color);
	}

	/* 日志监控 */
	.log-section {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	.log-preview {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.log-item {
		display: flex;
		align-items: center;
		padding: 16rpx;
		background-color: var(--page-bg-color);
		border-radius: 8rpx;
		gap: 16rpx;

		.log-time {
			font-size: 20rpx;
			color: var(--text-secondary-color);
			white-space: nowrap;
			min-width: 120rpx;
		}

		.log-content {
			flex: 1;
			font-size: 24rpx;
			color: var(--text-primary-color);
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.log-level {
			font-size: 20rpx;
			padding: 4rpx 8rpx;
			border-radius: 4rpx;
			font-weight: 500;

			&.level-info {
				background-color: #e0f2fe;
				color: #0369a1;
			}

			&.level-warn {
				background-color: #fef3c7;
				color: #d97706;
			}

			&.level-error {
				background-color: #fee2e2;
				color: #dc2626;
			}
		}
	}

	/* 版本管理弹窗 */
	.version-popup {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx 20rpx 0 0;
		padding: 32rpx;
		max-height: 80vh;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 32rpx;
		padding-bottom: 16rpx;
		border-bottom: 2rpx solid var(--border-color);

		.popup-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-primary-color);
		}

		.popup-close {
			font-size: 28rpx;
			color: #007aff;
		}
	}

	.version-list {
		display: flex;
		flex-direction: column;
		gap: 2rpx;
	}

	.version-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 16rpx;
		border-radius: 8rpx;
		transition: background-color 0.2s ease;

		.version-name {
			font-size: 28rpx;
			color: var(--text-primary-color);
		}

		.current-tag {
			font-size: 22rpx;
			color: #007aff;
			background-color: rgba(0, 122, 255, 0.1);
			padding: 4rpx 12rpx;
			border-radius: 12rpx;
		}
	}

	.version-hover {
		background-color: var(--page-bg-color);
	}

	/* 响应式调整 */
	@media (max-width: 360px) {
		.actions-grid {
			grid-template-columns: repeat(3, 1fr);
		}

		.version-info {
			flex-direction: column;
			gap: 24rpx;
		}
	}
</style>